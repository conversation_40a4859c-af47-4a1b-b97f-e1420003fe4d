
/**
 * 图片缓存工具
 * 用于缓存图片到本地，提高加载速度
 *
 * 注意：此功能仅在APP平台上可用，H5平台将直接返回原始URL
 *
 * 功能：
 * 1. 请求合并机制：对于相同URL的请求只发起一次网络请求
 * 2. 持久化缓存：确保缓存在应用重启后仍然有效
 * 3. 内存缓存：添加内存缓存层，减少文件系统访问
 * 4. 自动清理：定期清理过期和超量缓存
 */

// 检查是否为APP平台
const isAppPlatform = (): boolean => {
  // #ifdef APP-PLUS
  return true;
  // #endif
  // #ifndef H5
  return false;
  // #endif
}

// 缓存键前缀
const CACHE_KEY_PREFIX = '__yx_image_cache_'
// 缓存有效期（默认1天，单位：毫秒）
const CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000
// 最大重试次数
const MAX_RETRY_COUNT = 2
// 重试延迟（毫秒）
const RETRY_DELAY = 500
// 最大缓存数量
const MAX_CACHE_COUNT = 100
// 缓存统计信息键
const CACHE_STATS_KEY = '__yx_image_cache_stats__'
// 内存缓存映射表 - 用于快速访问
const memoryCache: Map<string, string> = new Map()

/**
 * 缓存信息接口
 */
interface CacheInfo {
  localPath: string; // 本地路径
  originUrl: string; // 原始URL
  timestamp: number; // 缓存时间戳
  lastAccessed?: number; // 最后访问时间
}

/**
 * 缓存统计信息接口
 */
interface CacheStats {
  hits: number;       // 缓存命中次数
  misses: number;     // 缓存未命中次数
  errors: number;     // 错误次数
  size: number;       // 缓存数量
  lastCleanup: number; // 最后清理时间
}

// 正在进行的请求映射表，用于请求合并
const pendingRequests: Map<string, Promise<string>> = new Map()

/**
 * 获取缓存键
 * @param url 图片URL
 * @returns 缓存键
 */
const getCacheKey = (url: string): string => {
  // 使用URL作为缓存键的一部分
  return `${CACHE_KEY_PREFIX}${encodeURIComponent(url)}`
}

/**
 * 获取或初始化缓存统计信息
 * @returns 缓存统计信息
 */
const getCacheStats = (): CacheStats => {
  const stats = uni.getStorageSync(CACHE_STATS_KEY) as CacheStats;
  if (!stats) {
    const newStats: CacheStats = {
      hits: 0,
      misses: 0,
      errors: 0,
      size: 0,
      lastCleanup: Date.now()
    };
    uni.setStorageSync(CACHE_STATS_KEY, newStats);
    return newStats;
  }
  return stats;
};

/**
 * 更新缓存统计信息
 * @param update 更新函数
 */
const updateCacheStats = (update: (stats: CacheStats) => void): void => {
  const stats = getCacheStats();
  update(stats);
  uni.setStorageSync(CACHE_STATS_KEY, stats);
};

/**
 * 清理过期或超量的缓存
 */
const cleanupCache = (): void => {
  if (!isAppPlatform()) {
    return;
  }

  try {
    const storage = uni.getStorageInfoSync();
    const keys = storage.keys || [];

    // 筛选出图片缓存的键
    const cacheKeys = keys.filter(key => key.startsWith(CACHE_KEY_PREFIX) && key !== CACHE_STATS_KEY);

    // 如果缓存数量未超过限制，只清理过期缓存
    if (cacheKeys.length <= MAX_CACHE_COUNT) {
      const now = Date.now();
      let expiredCount = 0;

      cacheKeys.forEach(key => {
        const cacheInfo: CacheInfo = uni.getStorageSync(key);
        if (cacheInfo && now - cacheInfo.timestamp > CACHE_EXPIRE_TIME) {
          // 删除本地缓存文件
          if (cacheInfo.localPath && !cacheInfo.localPath.startsWith('/static/') && !cacheInfo.localPath.includes('/uniapp_temp/')) {
            // #ifdef APP-PLUS
            try {
              plus.io.resolveLocalFileSystemURL(cacheInfo.localPath,
                (fileEntry) => {
                  fileEntry.remove(
                    () => {
                      console.log(`[图片缓存] 删除过期缓存文件: ${cacheInfo.localPath}`);
                    },
                    (e) => {
                      console.warn('删除过期缓存文件失败:', e);
                    }
                  );
                },
                (e) => {
                  console.warn('获取过期缓存文件失败:', e);
                }
              );
            } catch (e) {
              console.warn('删除过期缓存文件失败:', e);
            }
            // #endif
          }

          // 清除存储缓存
          uni.removeStorageSync(key);

          // 清除内存缓存
          if (cacheInfo.originUrl) {
            memoryCache.delete(cacheInfo.originUrl);
          }

          expiredCount++;
        }
      });

      if (expiredCount > 0) {
        console.log(`已清理${expiredCount}个过期缓存`);
      }

      // 更新统计信息
      updateCacheStats(stats => {
        stats.size = cacheKeys.length - expiredCount;
        stats.lastCleanup = now;
      });

      return;
    }

    // 如果缓存数量超过限制，按最后访问时间排序并清理
    const cacheEntries: Array<{key: string, info: CacheInfo}> = [];

    cacheKeys.forEach(key => {
      const info: CacheInfo = uni.getStorageSync(key);
      if (info) {
        cacheEntries.push({ key, info });
      }
    });

    // 按最后访问时间排序（从旧到新）
    cacheEntries.sort((a, b) => {
      const timeA = a.info.lastAccessed || a.info.timestamp;
      const timeB = b.info.lastAccessed || b.info.timestamp;
      return timeA - timeB;
    });

    // 删除超出限制的最旧缓存
    const toRemove = cacheEntries.slice(0, cacheEntries.length - MAX_CACHE_COUNT);
    toRemove.forEach(entry => {
      // 删除本地缓存文件
      if (entry.info.localPath && !entry.info.localPath.startsWith('/static/') && !entry.info.localPath.includes('/uniapp_temp/')) {
        // #ifdef APP-PLUS
        try {
          plus.io.resolveLocalFileSystemURL(entry.info.localPath,
            (fileEntry) => {
              fileEntry.remove(
                () => {
                  console.log(`[图片缓存] 删除最旧缓存文件: ${entry.info.localPath}`);
                },
                (e) => {
                  console.warn('删除缓存文件失败:', e);
                }
              );
            },
            (e) => {
              console.warn('获取缓存文件失败:', e);
            }
          );
        } catch (e) {
          console.warn('删除缓存文件失败:', e);
        }
        // #endif
      }

      // 清除存储缓存
      uni.removeStorageSync(entry.key);

      // 清除内存缓存
      if (entry.info.originUrl) {
        memoryCache.delete(entry.info.originUrl);
      }
    });

    console.log(`已清理${toRemove.length}个最旧缓存`);

    // 更新统计信息
    updateCacheStats(stats => {
      stats.size = Math.min(cacheEntries.length, MAX_CACHE_COUNT);
      stats.lastCleanup = Date.now();
    });
  } catch (error) {
    console.error('清理缓存失败:', error);
  }
};

/**
 * 获取缓存的图片路径
 * @param url 图片URL
 * @returns 本地缓存的图片路径，如果没有缓存则返回null
 */
const getCachedImagePath = (url: string): string | null => {
  if (!url) return null

  // 如果不是APP平台，直接返回null
  if (!isAppPlatform()) {
    return null;
  }

  // 首先检查内存缓存
  if (memoryCache.has(url)) {
    const cachedPath = memoryCache.get(url)
    if (cachedPath) {
      updateCacheStats(stats => { stats.hits++ });
      console.log(`[图片缓存] 内存缓存命中: ${url.substring(0, 50)}... -> ${cachedPath}`);
      return cachedPath
    }
  }

  const cacheKey = getCacheKey(url)
  const cacheInfo: CacheInfo = uni.getStorageSync(cacheKey)

  if (!cacheInfo || !cacheInfo.localPath) {
    updateCacheStats(stats => { stats.misses++ });
    console.log(`[图片缓存] 缓存未命中: ${url.substring(0, 50)}... (缓存信息不存在)`);
    return null
  }

  // 检查缓存是否过期
  const now = Date.now()
  if (now - cacheInfo.timestamp > CACHE_EXPIRE_TIME) {
    uni.removeStorageSync(cacheKey)
    // 同时清除内存缓存
    memoryCache.delete(url)
    updateCacheStats(stats => { stats.misses++ });
    console.log(`[图片缓存] 缓存未命中: ${url.substring(0, 50)}... (缓存已过期)`);
    return null
  }

  // 检查本地文件是否存在（对于持久化路径，我们信任缓存记录）
  // 如果是临时路径，则需要检查文件是否存在
  if (cacheInfo.localPath.includes('/uniapp_temp/')) {
    try {
      const fs = uni.getFileSystemManager()
      fs.accessSync(cacheInfo.localPath)
    } catch (error) {
      // 临时文件不存在，删除缓存记录
      uni.removeStorageSync(cacheKey)
      // 同时清除内存缓存
      memoryCache.delete(url)
      updateCacheStats(stats => { stats.misses++ });
      console.log(`[图片缓存] 缓存未命中: ${url.substring(0, 50)}... (临时文件不存在)`);
      return null
    }
  }

  // 更新最后访问时间
  cacheInfo.lastAccessed = now;
  uni.setStorageSync(cacheKey, cacheInfo);

  // 更新内存缓存
  memoryCache.set(url, cacheInfo.localPath);

  updateCacheStats(stats => { stats.hits++ });

  console.log(`[图片缓存] 存储缓存命中: ${url.substring(0, 50)}... -> ${cacheInfo.localPath}`);
  return cacheInfo.localPath
}

/**
 * 获取持久化缓存目录路径
 */
const getCacheDir = (): string => {
  if (!isAppPlatform()) {
    return '';
  }

  // #ifdef APP-PLUS
  // 使用应用的文档目录作为缓存目录，确保持久化存储
  const docPath = plus.io.convertLocalFileSystemURL('_doc');
  // 确保路径格式正确，避免双斜杠
  return `${docPath.replace(/\/$/, '')}/imageCache`;
  // #endif

  // #ifndef APP-PLUS
  return '';
  // #endif
}

/**
 * 确保缓存目录存在
 */
const ensureCacheDir = (): void => {
  if (!isAppPlatform()) {
    return;
  }

  // #ifdef APP-PLUS
  try {
    const cacheDir = getCacheDir();

    // 使用plus.io API检查和创建目录
    plus.io.resolveLocalFileSystemURL(cacheDir,
      () => {
        // 目录已存在，无需操作
        console.log(`[图片缓存] 缓存目录已存在: ${cacheDir}`);
      },
      () => {
        // 目录不存在，创建目录
        const parentDir = plus.io.convertLocalFileSystemURL('_doc');
        plus.io.resolveLocalFileSystemURL(parentDir,
          (parentEntry) => {
            parentEntry.getDirectory('imageCache', { create: true },
              () => {
                console.log(`[图片缓存] 创建缓存目录: ${cacheDir}`);
              },
              (createError) => {
                console.error('[图片缓存] 创建缓存目录失败:', createError);
              }
            );
          },
          (parentError) => {
            console.error('[图片缓存] 获取父目录失败:', parentError);
          }
        );
      }
    );
  } catch (error) {
    console.error('[图片缓存] 检查缓存目录失败:', error);
  }
  // #endif
}

/**
 * 简单的字符串hash函数
 */
const simpleHash = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return Math.abs(hash).toString(36);
}

/**
 * 生成缓存文件路径
 * @param url 原始URL
 * @returns 缓存文件的完整路径
 */
const generateCacheFilePath = (url: string): string => {
  const cacheDir = getCacheDir();

  // 从URL中提取文件扩展名
  let ext = '.jpg'; // 默认扩展名
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const lastDotIndex = pathname.lastIndexOf('.');
    if (lastDotIndex > 0) {
      ext = pathname.substring(lastDotIndex);
      // 只保留扩展名部分，去掉可能的查询参数
      const questionIndex = ext.indexOf('?');
      if (questionIndex > 0) {
        ext = ext.substring(0, questionIndex);
      }
    }
  } catch (e) {
    // URL解析失败时，尝试从URL字符串中提取扩展名
    const lastDotIndex = url.lastIndexOf('.');
    const lastSlashIndex = url.lastIndexOf('/');
    if (lastDotIndex > lastSlashIndex && lastDotIndex > 0) {
      ext = url.substring(lastDotIndex);
      // 只保留扩展名部分，去掉可能的查询参数
      const questionIndex = ext.indexOf('?');
      if (questionIndex > 0) {
        ext = ext.substring(0, questionIndex);
      }
    }
  }

  // 使用简单hash作为文件名，确保唯一性且文件系统友好
  const hash = simpleHash(url);
  return `${cacheDir}/${hash}${ext}`;
}

/**
 * 缓存图片到本地（带重试机制）
 * @param url 图片URL
 * @param retryCount 当前重试次数
 * @returns Promise<string> 返回本地图片路径
 */
const cacheImage = (url: string, retryCount = 0): Promise<string> => {
  // 如果不是APP平台，直接返回原始URL
  if (!isAppPlatform()) {
    return Promise.resolve(url);
  }

  // 检查是否已有相同URL的请求正在进行中
  if (pendingRequests.has(url)) {
    return pendingRequests.get(url)!;
  }

  const promise = new Promise<string>((resolve, reject) => {
    if (!url) {
      reject(new Error('URL不能为空'))
      return
    }

    // 先检查是否已缓存
    const cachedPath = getCachedImagePath(url)
    if (cachedPath) {
      resolve(cachedPath)
      return
    }

    // 确保缓存目录存在
    ensureCacheDir();

    // 获取图片信息并缓存
    uni.getImageInfo({
      src: url,
      success: (res) => {
        // #ifdef APP-PLUS
        const persistentPath = generateCacheFilePath(url);

        // 将临时文件复制到持久化目录（使用plus.io API）
        plus.io.resolveLocalFileSystemURL(res.path,
          (sourceEntry) => {
            const cacheDir = getCacheDir();
            plus.io.resolveLocalFileSystemURL(cacheDir,
              (targetDirEntry) => {
                // 生成文件名
                const fileName = persistentPath.substring(persistentPath.lastIndexOf('/') + 1);

                sourceEntry.copyTo(targetDirEntry, fileName,
                  (newEntry) => {
                    // 复制成功，使用持久化路径
                    let actualPath = newEntry.fullPath || persistentPath;

                    // 确保路径以file://开头，这样uni-app才能正确识别
                    if (!actualPath.startsWith('file://')) {
                      actualPath = `file://${actualPath}`;
                    }

                    const cacheKey = getCacheKey(url)
                    const cacheInfo: CacheInfo = {
                      localPath: actualPath, // 使用实际的持久化路径
                      originUrl: url,
                      timestamp: Date.now(),
                      lastAccessed: Date.now()
                    }

                    try {
                      // 保存缓存信息
                      uni.setStorageSync(cacheKey, cacheInfo)

                      // 更新内存缓存
                      memoryCache.set(url, actualPath)

                      // 更新缓存统计
                      updateCacheStats(stats => {
                        stats.size++;
                        // 如果缓存数量超过限制，触发清理
                        if (stats.size > MAX_CACHE_COUNT) {
                          setTimeout(() => {
                            cleanupCache();
                          }, 0);
                        }
                      });

                      console.log(`[图片缓存] 下载并缓存新图片: ${url.substring(0, 50)}... -> ${actualPath}`);
                    } catch (error) {
                      console.error('[图片缓存] 保存缓存信息失败:', error);
                    }

                    resolve(actualPath)
                  },
                  (copyError) => {
                    console.error('[图片缓存] 复制文件到持久化目录失败:', copyError);
                    // 复制失败时，仍然使用临时路径（向后兼容）
                    const cacheKey = getCacheKey(url)
                    const cacheInfo: CacheInfo = {
                      localPath: res.path,
                      originUrl: url,
                      timestamp: Date.now(),
                      lastAccessed: Date.now()
                    }

                    try {
                      // 保存缓存信息
                      uni.setStorageSync(cacheKey, cacheInfo)
                      // 更新内存缓存
                      memoryCache.set(url, res.path)
                      // 更新缓存统计
                      updateCacheStats(stats => { stats.size++; });
                      console.log(`[图片缓存] 使用临时路径缓存图片: ${url.substring(0, 50)}... -> ${res.path}`);
                    } catch (error) {
                      console.error('[图片缓存] 保存缓存信息失败:', error);
                    }

                    resolve(res.path)
                  }
                );
              },
              (targetDirError) => {
                console.error('[图片缓存] 获取目标目录失败:', targetDirError);
                // 获取目标目录失败，使用临时路径
                const cacheKey = getCacheKey(url)
                const cacheInfo: CacheInfo = {
                  localPath: res.path,
                  originUrl: url,
                  timestamp: Date.now(),
                  lastAccessed: Date.now()
                }

                try {
                  uni.setStorageSync(cacheKey, cacheInfo)
                  memoryCache.set(url, res.path)
                  updateCacheStats(stats => { stats.size++; });
                  console.log(`[图片缓存] 使用临时路径缓存图片: ${url.substring(0, 50)}... -> ${res.path}`);
                } catch (error) {
                  console.error('[图片缓存] 保存缓存信息失败:', error);
                }

                resolve(res.path)
              }
            );
          },
          (sourceError) => {
            console.error('[图片缓存] 获取源文件失败:', sourceError);
            // 获取源文件失败，使用临时路径
            const cacheKey = getCacheKey(url)
            const cacheInfo: CacheInfo = {
              localPath: res.path,
              originUrl: url,
              timestamp: Date.now(),
              lastAccessed: Date.now()
            }

            try {
              uni.setStorageSync(cacheKey, cacheInfo)
              memoryCache.set(url, res.path)
              updateCacheStats(stats => { stats.size++; });
              console.log(`[图片缓存] 使用临时路径缓存图片: ${url.substring(0, 50)}... -> ${res.path}`);
            } catch (error) {
              console.error('[图片缓存] 保存缓存信息失败:', error);
            }

            resolve(res.path)
          }
        );
        // #endif

        // #ifndef APP-PLUS
        // 非APP平台直接使用原始路径
        resolve(res.path);
        // #endif
      },
      fail: (err) => {
        console.error(`缓存图片失败 (${retryCount}/${MAX_RETRY_COUNT}):`, err)

        // 更新错误统计
        updateCacheStats(stats => { stats.errors++ });

        // 如果未达到最大重试次数，则重试
        if (retryCount < MAX_RETRY_COUNT) {
          console.log(`重试缓存图片 (${retryCount + 1}/${MAX_RETRY_COUNT}): ${url}`);

          // 延迟一段时间后重试
          setTimeout(() => {
            cacheImage(url, retryCount + 1)
              .then(resolve)
              .catch(reject);
          }, RETRY_DELAY);
        } else {
          console.log(`图片缓存失败，使用默认图片: ${url}`);
          resolve('/static/default-img.png');
        }
      },
      complete: () => {
        // 请求完成后，从pendingRequests中移除
        if (retryCount === 0) {
          pendingRequests.delete(url);
        }
      }
    })
  });

  // 只有第一次请求才添加到pendingRequests中
  if (retryCount === 0) {
    pendingRequests.set(url, promise);
  }

  return promise;
}



/**
 * 获取图片路径（优先使用缓存）
 * @param url 图片URL
 * @param options 选项
 * @returns Promise<string> 返回图片路径
 */
export const getImagePath = (
  url: string,
  options: {
    forceUpdate?: boolean;
  } = {}
): Promise<string> => {
  const { forceUpdate = false } = options;

  if (!url) return Promise.resolve('');

  // 如果是本地路径，则直接返回
  if (url.startsWith('data:') || url.startsWith('file://') || url.startsWith('/')) {
    return Promise.resolve(url);
  }

  // 如果强制更新，直接下载
  if (forceUpdate) {
    return cacheImage(url);
  }

  // 先检查缓存
  const cachedPath = getCachedImagePath(url);
  if (cachedPath) {
    return Promise.resolve(cachedPath);
  }

  // 缓存不存在，下载并缓存
  return cacheImage(url);
}

/**
 * 清除指定URL的图片缓存
 * @param url 图片URL
 */
export const clearImageCache = (url: string): void => {
  if (!url) return;

  if (!isAppPlatform()) {
    return;
  }

  const cacheKey = getCacheKey(url);
  const cacheInfo: CacheInfo = uni.getStorageSync(cacheKey);

  // 删除本地缓存文件
  if (cacheInfo && cacheInfo.localPath && !cacheInfo.localPath.startsWith('/static/') && !cacheInfo.localPath.includes('/uniapp_temp/')) {
    // #ifdef APP-PLUS
    try {
      plus.io.resolveLocalFileSystemURL(cacheInfo.localPath,
        (fileEntry) => {
          fileEntry.remove(
            () => {
              console.log(`[图片缓存] 删除指定缓存文件: ${cacheInfo.localPath}`);
            },
            (e) => {
              console.warn('删除缓存文件失败:', e);
            }
          );
        },
        (e) => {
          console.warn('获取缓存文件失败:', e);
        }
      );
    } catch (e) {
      console.warn('删除缓存文件失败:', e);
    }
    // #endif
  }

  // 清除存储缓存
  uni.removeStorageSync(cacheKey);

  // 清除内存缓存
  memoryCache.delete(url);
}

/**
 * 清除所有图片缓存
 */
export const clearAllImageCache = (): void => {
  if (!isAppPlatform()) {
    return;
  }

  try {
    const storage = uni.getStorageInfoSync();
    const keys = storage.keys || [];

    // 筛选出图片缓存的键
    const cacheKeys = keys.filter(key => key.startsWith(CACHE_KEY_PREFIX) && key !== CACHE_STATS_KEY);

    // 删除所有图片缓存
    cacheKeys.forEach(key => {
      try {
        // 获取缓存信息以获取原始URL
        const cacheInfo: CacheInfo = uni.getStorageSync(key);
        if (cacheInfo) {
          // 删除本地缓存文件
          if (cacheInfo.localPath && !cacheInfo.localPath.startsWith('/static/') && !cacheInfo.localPath.includes('/uniapp_temp/')) {
            // #ifdef APP-PLUS
            try {
              plus.io.resolveLocalFileSystemURL(cacheInfo.localPath,
                (fileEntry) => {
                  fileEntry.remove(
                    () => {
                      console.log(`[图片缓存] 删除所有缓存文件: ${cacheInfo.localPath}`);
                    },
                    (e) => {
                      console.warn('删除缓存文件失败:', e);
                    }
                  );
                },
                (e) => {
                  console.warn('获取缓存文件失败:', e);
                }
              );
            } catch (e) {
              console.warn('删除缓存文件失败:', e);
            }
            // #endif
          }

          // 清除内存缓存
          if (cacheInfo.originUrl) {
            memoryCache.delete(cacheInfo.originUrl);
          }
        }
        // 删除存储缓存
        uni.removeStorageSync(key);
      } catch (e) {
        console.error('删除缓存项失败:', e);
      }
    });

    // 清空内存缓存
    memoryCache.clear();

    // 重置统计信息
    updateCacheStats(stats => {
      stats.hits = 0;
      stats.misses = 0;
      stats.errors = 0;
      stats.size = 0;
      stats.lastCleanup = Date.now();
    });

    console.log(`已清除${cacheKeys.length}个图片缓存`);
  } catch (error) {
    console.error('清除图片缓存失败:', error);
  }
}

/**
 * 获取缓存统计信息
 * @returns 缓存统计信息
 */
export const getCacheStatistics = (): CacheStats => {
  return getCacheStats();
}

/**
 * 手动触发缓存清理
 */
export const triggerCacheCleanup = (): void => {
  cleanupCache();
}



/**
 * 初始化缓存系统
 * 预加载缓存信息到内存中
 */
export const initImageCache = (): void => {
  if (!isAppPlatform()) {
    console.log('[图片缓存] 非APP平台，跳过图片缓存初始化');
    return;
  }

  try {
    console.log('[图片缓存] 初始化图片缓存系统...');
    const storage = uni.getStorageInfoSync();
    const keys = storage.keys || [];

    // 筛选出图片缓存的键
    const cacheKeys = keys.filter(key => key.startsWith(CACHE_KEY_PREFIX) && key !== CACHE_STATS_KEY);
    console.log(`[图片缓存] 发现 ${cacheKeys.length} 个缓存记录`);

    // 清空内存缓存
    memoryCache.clear();

    // 加载有效的缓存到内存中
    let validCount = 0;
    const now = Date.now();

    cacheKeys.forEach(key => {
      try {
        const cacheInfo: CacheInfo = uni.getStorageSync(key);

        // 检查缓存是否有效
        if (cacheInfo && cacheInfo.localPath && cacheInfo.originUrl &&
            now - cacheInfo.timestamp <= CACHE_EXPIRE_TIME) {

          // 对于持久化路径，我们信任缓存记录；对于临时路径，检查文件是否存在
          if (cacheInfo.localPath.includes('/uniapp_temp/')) {
            try {
              const fs = uni.getFileSystemManager();
              fs.accessSync(cacheInfo.localPath);
              // 临时文件存在，加载到内存缓存
              memoryCache.set(cacheInfo.originUrl, cacheInfo.localPath);
              validCount++;
            } catch (e) {
              // 临时文件不存在，删除缓存
              uni.removeStorageSync(key);
            }
          } else {
            // 持久化文件，直接加载到内存缓存
            memoryCache.set(cacheInfo.originUrl, cacheInfo.localPath);
            validCount++;
          }
        } else if (cacheInfo) {
          // 缓存已过期，删除
          uni.removeStorageSync(key);
        }
      } catch (e) {
        console.error('加载缓存项失败:', e);
      }
    });

    console.log(`[图片缓存] 初始化完成，已加载 ${validCount}/${cacheKeys.length} 个有效缓存`);

    // 获取并显示缓存统计信息
    const stats = getCacheStats();
    console.log(`[图片缓存] 统计信息 - 命中: ${stats.hits}, 未命中: ${stats.misses}, 错误: ${stats.errors}, 大小: ${stats.size}`);

    // 如果缓存数量超过限制，触发清理
    if (cacheKeys.length > MAX_CACHE_COUNT) {
      console.log(`[图片缓存] 缓存数量(${cacheKeys.length})超过限制(${MAX_CACHE_COUNT})，将触发清理`);
      setTimeout(() => {
        cleanupCache();
      }, 100);
    }
  } catch (error) {
    console.error('[图片缓存] 初始化失败:', error);
  }
};

// 自动初始化缓存
setTimeout(() => {
  initImageCache();
}, 0);

export default {
  getImagePath,
  clearImageCache,
  clearAllImageCache,
  getCacheStatistics,
  triggerCacheCleanup,
  initImageCache
}
